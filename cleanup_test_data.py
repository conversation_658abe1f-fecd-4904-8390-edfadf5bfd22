#!/usr/bin/env python3
"""
清理测试数据脚本
安全地删除数据库中的测试任务记录
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from sqlalchemy.orm import sessionmaker
from app.core.database import engine
from app.models.task import Task
from app.models.user import User
import shutil

def cleanup_test_data():
    """清理测试数据"""
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    with SessionLocal() as db:
        print("=== 开始清理测试数据 ===")
        
        # 1. 查找所有测试任务
        test_tasks = []
        
        # 查找标题包含"测试任务"的任务
        test_task_pattern_tasks = db.query(Task).filter(Task.title.like('测试任务%')).all()
        test_tasks.extend(test_task_pattern_tasks)
        
        # 查找特定的测试任务
        specific_test_tasks = db.query(Task).filter(Task.title.in_([
            '测试音频元数据API'
        ])).all()
        test_tasks.extend(specific_test_tasks)
        
        # 去重
        test_task_ids = list(set([task.id for task in test_tasks]))
        test_tasks = [db.query(Task).filter(Task.id == task_id).first() for task_id in test_task_ids]
        
        print(f"找到 {len(test_tasks)} 个测试任务:")
        for task in test_tasks:
            print(f"  - 任务ID: {task.id}, 标题: {task.title}, 用户ID: {task.user_id}, 状态: {task.status.value}")
        
        if not test_tasks:
            print("没有找到测试任务，无需清理")
            return
        
        # 2. 确认删除
        print(f"\n准备删除 {len(test_tasks)} 个测试任务")
        confirm = input("确认删除这些测试任务吗？(输入 'yes' 确认): ")
        
        if confirm.lower() != 'yes':
            print("取消删除操作")
            return
        
        # 3. 删除任务相关的文件
        deleted_files_count = 0
        for task in test_tasks:
            # 删除任务相关的音频文件目录
            task_dir = Path(f"data/users/{task.user_id}/tasks/{task.id}")
            if task_dir.exists():
                try:
                    shutil.rmtree(task_dir)
                    print(f"  ✅ 删除任务目录: {task_dir}")
                    deleted_files_count += 1
                except Exception as e:
                    print(f"  ❌ 删除任务目录失败 {task_dir}: {e}")
        
        # 4. 从数据库中删除任务记录
        deleted_tasks_count = 0
        for task in test_tasks:
            try:
                db.delete(task)
                deleted_tasks_count += 1
                print(f"  ✅ 删除任务记录: ID {task.id} - {task.title}")
            except Exception as e:
                print(f"  ❌ 删除任务记录失败 ID {task.id}: {e}")
        
        # 5. 提交更改
        try:
            db.commit()
            print(f"\n=== 清理完成 ===")
            print(f"删除了 {deleted_tasks_count} 个任务记录")
            print(f"删除了 {deleted_files_count} 个任务目录")
        except Exception as e:
            db.rollback()
            print(f"❌ 提交更改失败: {e}")
            return False
        
        # 6. 验证清理结果
        print("\n=== 验证清理结果 ===")
        remaining_test_tasks = db.query(Task).filter(Task.title.like('测试任务%')).all()
        remaining_api_test_tasks = db.query(Task).filter(Task.title == '测试音频元数据API').all()
        
        total_remaining = len(remaining_test_tasks) + len(remaining_api_test_tasks)
        if total_remaining == 0:
            print("✅ 所有测试任务已成功清理")
        else:
            print(f"⚠️ 仍有 {total_remaining} 个测试任务未清理")
        
        # 7. 显示剩余的真实任务统计
        all_remaining_tasks = db.query(Task).all()
        print(f"\n=== 剩余任务统计 ===")
        print(f"总任务数: {len(all_remaining_tasks)}")
        
        # 按用户统计
        user_task_counts = {}
        for task in all_remaining_tasks:
            user_id = task.user_id
            if user_id not in user_task_counts:
                user_task_counts[user_id] = {'total': 0, 'completed': 0, 'pending': 0, 'failed': 0}
            user_task_counts[user_id]['total'] += 1
            user_task_counts[user_id][task.status.value] += 1
        
        for user_id, counts in user_task_counts.items():
            user = db.query(User).filter(User.id == user_id).first()
            user_email = user.email if user else f"用户ID {user_id}"
            print(f"用户 {user_email}: {counts['total']} 个任务 (已完成: {counts['completed']}, 处理中: {counts['pending']}, 失败: {counts['failed']})")
        
        return True

if __name__ == "__main__":
    try:
        success = cleanup_test_data()
        if success:
            print("\n🎉 测试数据清理成功完成！")
        else:
            print("\n❌ 测试数据清理失败")
    except Exception as e:
        print(f"\n❌ 清理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
