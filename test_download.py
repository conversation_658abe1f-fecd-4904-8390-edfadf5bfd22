#!/usr/bin/env python3
"""
测试音频下载功能的脚本
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_audio_download():
    """测试音频下载功能"""
    
    # 1. 跳过注册，直接使用现有用户
    print("1. 使用现有用户...")

    # 2. 登录获取token
    print("\n2. 登录获取token...")
    login_data = {
        "email": "<EMAIL>",
        "password": "123456"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✓ 登录成功，获取到token: {token[:20]}...")
        else:
            print(f"✗ 登录失败: {response.status_code} - {response.text}")
            return
    except Exception as e:
        print(f"✗ 登录请求失败: {e}")
        return
    
    # 3. 获取任务列表
    print("\n3. 获取任务列表...")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/api/tasks", headers=headers)
        if response.status_code == 200:
            tasks = response.json()
            print(f"✓ 获取到 {len(tasks)} 个任务")
            
            # 查找已完成的任务
            completed_tasks = [task for task in tasks if task.get("status") == "completed"]
            if completed_tasks:
                task_id = completed_tasks[0]["id"]
                print(f"✓ 找到已完成任务: {task_id}")
            else:
                print("✗ 没有找到已完成的任务，无法测试下载功能")
                return
        else:
            print(f"✗ 获取任务列表失败: {response.status_code} - {response.text}")
            return
    except Exception as e:
        print(f"✗ 获取任务列表请求失败: {e}")
        return
    
    # 4. 测试下载接口（使用Authorization头）
    print(f"\n4. 测试下载接口（使用Authorization头）...")
    try:
        response = requests.get(f"{BASE_URL}/api/audio/{task_id}/download", headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        print(f"Content-Length: {response.headers.get('Content-Length', 'N/A')}")
        print(f"Content-Disposition: {response.headers.get('Content-Disposition', 'N/A')}")
        
        if response.status_code == 200:
            if response.headers.get('Content-Type') == 'audio/mpeg':
                print("✓ 下载接口返回正确的音频文件")
            else:
                print(f"✗ 下载接口返回错误的Content-Type: {response.headers.get('Content-Type')}")
                print(f"响应内容前100字符: {response.text[:100]}")
        else:
            print(f"✗ 下载接口失败: {response.text}")
    except Exception as e:
        print(f"✗ 下载请求失败: {e}")
    
    # 5. 测试下载接口（使用token查询参数）
    print(f"\n5. 测试下载接口（使用token查询参数）...")
    try:
        response = requests.get(f"{BASE_URL}/api/audio/{task_id}/download?token={token}")
        print(f"状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        print(f"Content-Length: {response.headers.get('Content-Length', 'N/A')}")
        print(f"Content-Disposition: {response.headers.get('Content-Disposition', 'N/A')}")
        
        if response.status_code == 200:
            if response.headers.get('Content-Type') == 'audio/mpeg':
                print("✓ 下载接口（token参数）返回正确的音频文件")
            else:
                print(f"✗ 下载接口（token参数）返回错误的Content-Type: {response.headers.get('Content-Type')}")
                print(f"响应内容前100字符: {response.text[:100]}")
        else:
            print(f"✗ 下载接口（token参数）失败: {response.text}")
    except Exception as e:
        print(f"✗ 下载请求（token参数）失败: {e}")

if __name__ == "__main__":
    test_audio_download()
