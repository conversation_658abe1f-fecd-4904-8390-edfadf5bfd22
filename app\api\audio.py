"""
音频库API
提供音频文件管理和播放相关功能
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, Header
from fastapi.responses import FileResponse, StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
import os
from pathlib import Path

from app.core.database import get_db
from app.core.auth import get_current_active_user
from app.models.user import User
from app.models.task import Task, TaskStatus
from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger(__name__)

router = APIRouter()


@router.get("/audio-library")
async def get_audio_library(
    sort: str = "recent",  # recent, name, duration
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户音频库"""
    try:
        # 查询用户已完成的任务
        query = db.query(Task).filter(
            Task.user_id == current_user.id,
            Task.status == TaskStatus.COMPLETED
        )
        
        # 排序
        if sort == "name":
            query = query.order_by(Task.title)
        elif sort == "duration":
            # 假设有duration字段，如果没有则按创建时间排序
            query = query.order_by(Task.created_at.desc())
        else:  # recent
            query = query.order_by(Task.created_at.desc())
        
        tasks = query.all()
        
        # 转换为音频库格式
        audio_books = []
        for task in tasks:
            # 构建音频文件路径 - 使用正确的Task模型字段
            audio_url = None
            playlist_url = None

            # 检查是否有音频文件或播放列表
            if task.audio_files or task.playlist_url:
                audio_url = f"/api/audio/{task.id}"
                playlist_url = task.playlist_url

            # 格式化时长信息 - 支持float类型的duration
            duration_formatted = None
            if task.total_duration is not None:
                # 确保duration是数字类型，并转换为整数秒数进行格式化
                total_seconds = int(float(task.total_duration))
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                seconds = total_seconds % 60
                if hours > 0:
                    duration_formatted = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                else:
                    duration_formatted = f"{minutes:02d}:{seconds:02d}"

            # 格式化文件大小
            size_formatted = None
            if task.file_size:
                if task.file_size < 1024:
                    size_formatted = f"{task.file_size} B"
                elif task.file_size < 1024 * 1024:
                    size_formatted = f"{task.file_size / 1024:.1f} KB"
                else:
                    size_formatted = f"{task.file_size / (1024 * 1024):.1f} MB"

            # 计算章节数和增强版状态 - 适配新的音频合并格式
            total_chapters = 1  # 默认为1
            is_enhanced = False  # 默认为普通版

            if task.audio_files:
                if isinstance(task.audio_files, list):
                    # 新格式：单个合并音频文件的列表，或旧格式的多个分段文件
                    if len(task.audio_files) == 1 and isinstance(task.audio_files[0], dict):
                        # 检查是否是合并音频文件（包含segments_merged字段）
                        merged_segments = task.audio_files[0].get('segments_merged')
                        if merged_segments:
                            # 这是合并后的单个音频文件，不应该显示为增强版
                            total_chapters = 1  # 显示为单个文件
                            is_enhanced = False  # 不是增强版
                        else:
                            # 旧格式或真正的单个文件
                            total_chapters = len(task.audio_files)
                            is_enhanced = len(task.audio_files) > 1
                    else:
                        # 多个分段文件，确实是增强版
                        total_chapters = len(task.audio_files)
                        is_enhanced = len(task.audio_files) > 1
                else:
                    total_chapters = 1
                    is_enhanced = False

            # 如果有播放列表URL且不为None，也可能是增强版（但要排除合并后的情况）
            if task.playlist_url and not is_enhanced:
                # 只有在没有被识别为合并文件的情况下才考虑playlist_url
                if not (task.audio_files and isinstance(task.audio_files, list) and
                       len(task.audio_files) == 1 and isinstance(task.audio_files[0], dict) and
                       task.audio_files[0].get('segments_merged')):
                    is_enhanced = True

            audio_book = {
                "id": task.id,
                "title": task.title or "未命名",
                "filename": task.original_filename or "",
                "status": task.status.value,
                "audioUrl": audio_url,
                "playlistUrl": playlist_url,
                "totalChapters": total_chapters,
                "isEnhanced": is_enhanced,
                "createdAt": task.created_at.isoformat(),
                "updatedAt": task.updated_at.isoformat(),
                "duration": duration_formatted,
                "durationSeconds": task.total_duration,
                "size": size_formatted,
                "fileSize": task.file_size,
                "taskType": task.task_type.value
            }
            audio_books.append(audio_book)
        
        return {
            "success": True,
            "audioBooks": audio_books,
            "total": len(audio_books),
            "statistics": {
                "totalTasks": len(audio_books),
                "successTasks": len(audio_books),
                "audioTasks": len(audio_books)
            }
        }
        
    except Exception as e:
        logger.error(f"获取音频库失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取音频库失败"
        )


@router.get("/audio/{task_id}")
async def get_audio_file(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取音频文件 - 返回播放列表信息"""
    try:
        # 验证任务所有权
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user.id,
            Task.status == TaskStatus.COMPLETED
        ).first()

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="音频文件不存在"
            )

        # 计算章节数 - 适配新的音频合并格式
        total_chapters = 1
        if task.audio_files:
            if isinstance(task.audio_files, list):
                if len(task.audio_files) == 1 and isinstance(task.audio_files[0], dict):
                    # 检查是否是合并音频文件
                    merged_segments = task.audio_files[0].get('segments_merged')
                    if merged_segments:
                        # 对于合并后的音频文件，返回1表示单个文件
                        total_chapters = 1
                    else:
                        total_chapters = len(task.audio_files)
                else:
                    total_chapters = len(task.audio_files)

        # 返回播放列表信息和音频文件列表
        return {
            "task_id": task.id,
            "title": task.title,
            "status": task.status.value,
            "playlist_url": task.playlist_url,
            "audio_files": task.audio_files,
            "total_duration": task.total_duration,
            "total_chapters": total_chapters
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取音频文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取音频文件失败"
        )


@router.get("/audio/{task_id}/file/{segment_index}")
async def get_audio_segment(
    task_id: int,
    segment_index: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取特定音频段落文件"""
    try:
        # 验证任务所有权
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user.id,
            Task.status == TaskStatus.COMPLETED
        ).first()

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="音频文件不存在"
            )

        # 构建音频文件路径
        audio_file_path = Path(settings.data_dir) / "users" / str(current_user.id) / "tasks" / str(task_id) / f"segment_{segment_index:04d}.mp3"

        if not audio_file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="音频文件不存在"
            )

        # 返回音频文件
        return FileResponse(
            path=str(audio_file_path),
            media_type="audio/mpeg",
            filename=f"segment_{segment_index:04d}.mp3"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取音频段落失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取音频段落失败"
        )


@router.get("/audio/{task_id}/playlist")
async def get_playlist(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取播放列表文件"""
    try:
        # 验证任务所有权
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user.id,
            Task.status == TaskStatus.COMPLETED
        ).first()

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="播放列表不存在"
            )

        # 构建播放列表文件路径
        playlist_path = Path(settings.data_dir) / "users" / str(current_user.id) / "tasks" / str(task_id) / f"playlist_{task_id}.json"

        if not playlist_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="播放列表文件不存在"
            )

        # 返回播放列表文件
        return FileResponse(
            path=str(playlist_path),
            media_type="application/json",
            filename=f"playlist_{task_id}.json"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取播放列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取播放列表失败"
        )


async def get_current_user_from_token_or_header(
    token: Optional[str] = Query(None),
    authorization: Optional[str] = Header(None),
    db: Session = Depends(get_db)
) -> User:
    """从查询参数或Authorization头获取当前用户"""
    from app.core.auth import get_current_user_from_token

    # 优先使用查询参数中的token（用于音频流）
    if token:
        return await get_current_user_from_token(token, db)

    # 回退到Authorization头
    if authorization and authorization.startswith("Bearer "):
        token = authorization.split(" ")[1]
        return await get_current_user_from_token(token, db)

    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="未提供有效的认证信息"
    )

@router.get("/audio/{task_id}/stream")
async def stream_audio_file(
    task_id: int,
    current_user: User = Depends(get_current_user_from_token_or_header),
    db: Session = Depends(get_db)
):
    """直接返回音频文件流"""
    try:
        # 验证任务所有权
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user.id,
            Task.status == TaskStatus.COMPLETED
        ).first()

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="音频文件不存在"
            )

        # 构建音频文件路径 - 优先查找合并后的音频文件
        user_task_dir = Path(settings.data_dir) / "users" / str(current_user.id) / "tasks" / str(task_id)

        # 尝试多种可能的音频文件名
        possible_files = [
            f"original_{task_id}.mp3",  # 合并后的音频文件
            f"merged_audio_{task_id}.mp3",  # 另一种合并文件命名
            f"audio_{task_id}.mp3",  # 基本命名
            "merged_audio.mp3",  # 通用合并文件名
            "audio.mp3"  # 通用文件名
        ]

        audio_file_path = None
        for filename in possible_files:
            potential_path = user_task_dir / filename
            if potential_path.exists():
                audio_file_path = potential_path
                break

        if not audio_file_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="音频文件不存在"
            )

        # 返回音频文件
        return FileResponse(
            path=str(audio_file_path),
            media_type="audio/mpeg",
            filename=f"{task.title or f'audio_{task_id}'}.mp3"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取音频文件流失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取音频文件流失败"
        )


@router.get("/audio/{task_id}/download")
async def download_audio_file(
    task_id: int,
    current_user: User = Depends(get_current_user_from_token_or_header),
    db: Session = Depends(get_db)
):
    """下载音频文件"""
    try:
        # 验证任务所有权
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user.id,
            Task.status == TaskStatus.COMPLETED
        ).first()

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="音频文件不存在"
            )

        # 构建音频文件路径 - 优先查找合并后的音频文件
        user_task_dir = Path(settings.data_dir) / "users" / str(current_user.id) / "tasks" / str(task_id)

        # 尝试多种可能的音频文件名
        possible_files = [
            f"original_{task_id}.mp3",  # 合并后的音频文件
            f"merged_audio_{task_id}.mp3",  # 另一种合并文件命名
            f"audio_{task_id}.mp3",  # 基本命名
            "merged_audio.mp3",  # 通用合并文件名
            "audio.mp3"  # 通用文件名
        ]

        audio_file_path = None
        for filename in possible_files:
            potential_path = user_task_dir / filename
            if potential_path.exists():
                audio_file_path = potential_path
                break

        if not audio_file_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="音频文件不存在"
            )

        # 生成安全的文件名
        safe_title = task.title or f"audio_{task_id}"
        # 清理文件名中的特殊字符
        safe_filename = "".join(c for c in safe_title if c.isalnum() or c in (' ', '-', '_', '.')).strip()
        if not safe_filename:
            safe_filename = f"audio_{task_id}"

        # 确保文件名以.mp3结尾
        if not safe_filename.lower().endswith('.mp3'):
            safe_filename += '.mp3'

        # 返回音频文件用于下载
        return FileResponse(
            path=str(audio_file_path),
            media_type="audio/mpeg",
            filename=safe_filename,
            headers={
                "Content-Disposition": f"attachment; filename=\"{safe_filename}\"",
                "Cache-Control": "no-cache"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载音频文件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="下载音频文件失败"
        )


@router.get("/audio/{task_id}/metadata")
async def get_audio_metadata(
    task_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取音频元数据"""
    try:
        task = db.query(Task).filter(
            Task.id == task_id,
            Task.user_id == current_user.id
        ).first()

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        return {
            "task_id": task.id,
            "title": task.title,
            "status": task.status.value,
            "progress": task.progress,
            "created_at": task.created_at.isoformat(),
            "updated_at": task.updated_at.isoformat(),
            "processing_mode": getattr(task, 'processing_mode', 'standard'),
            "voice_settings": task.voice_settings or {},
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取音频元数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取音频元数据失败"
        )
