<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务渲染调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        .task-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .btn { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>任务渲染调试页面</h1>
    
    <div class="debug-section">
        <h2>模拟任务数据</h2>
        <button onclick="testTaskRendering()">测试任务渲染</button>
        <div id="test-results"></div>
    </div>
    
    <div class="debug-section">
        <h2>hasAudioFiles 方法测试</h2>
        <button onclick="testHasAudioFiles()">测试 hasAudioFiles 方法</button>
        <div id="audio-test-results"></div>
    </div>

    <script>
        // 模拟任务数据
        const mockTasks = [
            {
                id: 1000,
                title: "Dortha_Ledner",
                status: "completed",
                audioUrl: null,
                audio_url: null,
                playlistUrl: null,
                playlist_url: null,
                downloadUrl: null,
                download_url: null
            },
            {
                id: 998,
                title: "Elsie Lowe", 
                status: "completed",
                audioUrl: null,
                audio_url: null,
                playlistUrl: null,
                playlist_url: null,
                downloadUrl: null,
                download_url: null
            },
            {
                id: 13,
                title: "Elsie Lowe",
                status: "failed",
                audioUrl: null,
                audio_url: null,
                playlistUrl: null,
                playlist_url: "/files/users/4/tasks/13/playlist_13.json",
                downloadUrl: null,
                download_url: null
            }
        ];

        // 复制 hasAudioFiles 方法
        function hasAudioFiles(task) {
            console.log('检查任务音频文件:', task);
            
            // 检查各种可能的音频URL字段
            if (task.audioUrl || task.audio_url) {
                console.log('找到 audioUrl:', task.audioUrl || task.audio_url);
                return true;
            }
            
            // 检查播放列表URL（增强版音频）
            if (task.playlistUrl || task.playlist_url) {
                console.log('找到 playlistUrl:', task.playlistUrl || task.playlist_url);
                return true;
            }
            
            // 检查音频路径
            if (task.audioPath || task.audio_path) {
                console.log('找到 audioPath:', task.audioPath || task.audio_path);
                return true;
            }
            
            // 检查下载URL
            if (task.downloadUrl || task.download_url) {
                console.log('找到 downloadUrl:', task.downloadUrl || task.download_url);
                return true;
            }
            
            // 对于已完成的任务，假设都有音频文件（通过API流式传输）
            if (task.status === 'completed') {
                console.log('任务已完成，假设有音频文件');
                return true;
            }
            
            console.log('未找到音频文件');
            return false;
        }

        function testHasAudioFiles() {
            const resultsDiv = document.getElementById('audio-test-results');
            let html = '<h3>hasAudioFiles 测试结果:</h3>';
            
            mockTasks.forEach(task => {
                const hasAudio = hasAudioFiles(task);
                html += `
                    <div class="task-item">
                        <strong>任务 ${task.id} (${task.title})</strong><br>
                        状态: ${task.status}<br>
                        有音频文件: <strong>${hasAudio ? '是' : '否'}</strong><br>
                        应该显示播放按钮: <strong>${task.status === 'completed' && hasAudio ? '是' : '否'}</strong>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }

        function testTaskRendering() {
            const resultsDiv = document.getElementById('test-results');
            let html = '<h3>任务渲染测试结果:</h3>';
            
            mockTasks.forEach(task => {
                const shouldShowActions = task.status === 'completed' && hasAudioFiles(task);
                html += `
                    <div class="task-item">
                        <h4>${task.title}</h4>
                        <p>状态: ${task.status}</p>
                        <p>应该显示操作按钮: <strong>${shouldShowActions ? '是' : '否'}</strong></p>
                        ${shouldShowActions ? `
                            <div style="margin-top: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                <button class="btn" onclick="alert('播放任务 ${task.id}')">
                                    播放音频
                                </button>
                                <button class="btn" onclick="alert('下载任务 ${task.id}')">
                                    下载音频
                                </button>
                            </div>
                        ` : ''}
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            testHasAudioFiles();
            testTaskRendering();
        };
    </script>
</body>
</html>
