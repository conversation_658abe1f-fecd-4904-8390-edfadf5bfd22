#!/usr/bin/env python3
"""
检查API响应数据格式的脚本
用于诊断下载按钮不显示的问题
"""

import requests
import json
import sys
from pathlib import Path

def check_api_response():
    """检查API响应数据格式"""
    
    # API基础URL
    base_url = "http://localhost:8000"
    
    print("🔍 检查有声书项目API响应数据格式...")
    print("=" * 60)
    
    try:
        # 1. 检查健康状态
        print("1. 检查API健康状态...")
        health_response = requests.get(f"{base_url}/api/health")
        if health_response.status_code == 200:
            print("✅ API服务正常运行")
        else:
            print(f"❌ API服务异常: {health_response.status_code}")
            return
        
        # 2. 检查任务API端点（不需要认证的测试）
        print("\n2. 检查任务API端点...")
        
        # 尝试访问任务列表（可能需要认证）
        tasks_response = requests.get(f"{base_url}/api/tasks")
        print(f"任务列表API状态码: {tasks_response.status_code}")
        
        if tasks_response.status_code == 401:
            print("⚠️  需要认证才能访问任务数据")
            print("请在浏览器中登录后，从开发者工具中获取认证令牌")
            
            # 提供手动测试的JavaScript代码
            print("\n📋 请在浏览器控制台中运行以下代码来检查任务数据:")
            print("""
// 在浏览器控制台中运行此代码
async function checkTaskData() {
    try {
        const response = await fetch('/api/tasks', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('access_token')}`
            }
        });
        
        if (response.ok) {
            const tasks = await response.json();
            console.log('📊 任务数据:', tasks);
            
            if (tasks && tasks.length > 0) {
                const task = tasks[0];
                console.log('🔍 第一个任务详情:');
                console.log('- ID:', task.id);
                console.log('- 标题:', task.title || task.original_filename);
                console.log('- 状态:', task.status);
                console.log('- 状态类型:', typeof task.status);
                console.log('- 音频文件:', task.audio_files);
                console.log('- 播放列表URL:', task.playlist_url);
                console.log('- 总时长:', task.total_duration);
                
                // 检查下载按钮显示逻辑
                console.log('\\n🔍 下载按钮显示逻辑检查:');
                console.log('- 状态是否为completed:', task.status === 'completed');
                console.log('- 是否有音频文件:', !!(task.audio_files || task.playlist_url));
                
                const shouldShow = task.status === 'completed' && !!(task.audio_files || task.playlist_url);
                console.log('- 应该显示下载按钮:', shouldShow);
                
                if (!shouldShow) {
                    console.log('❌ 下载按钮不显示的原因:');
                    if (task.status !== 'completed') {
                        console.log('  - 任务状态不是 "completed"，当前状态:', task.status);
                    }
                    if (!(task.audio_files || task.playlist_url)) {
                        console.log('  - 没有音频文件或播放列表URL');
                    }
                }
            } else {
                console.log('📭 没有找到任务数据');
            }
        } else {
            console.error('❌ 获取任务数据失败:', response.status);
        }
    } catch (error) {
        console.error('❌ 检查任务数据时出错:', error);
    }
}

checkTaskData();
            """)
            
        elif tasks_response.status_code == 200:
            try:
                tasks_data = tasks_response.json()
                print("✅ 成功获取任务数据")
                print(f"任务数量: {len(tasks_data) if isinstance(tasks_data, list) else '未知'}")
                
                if isinstance(tasks_data, list) and len(tasks_data) > 0:
                    task = tasks_data[0]
                    print(f"\n📋 第一个任务详情:")
                    print(f"- ID: {task.get('id', '未知')}")
                    print(f"- 标题: {task.get('title', task.get('original_filename', '未知'))}")
                    print(f"- 状态: {task.get('status', '未知')}")
                    print(f"- 状态类型: {type(task.get('status', ''))}")
                    print(f"- 音频文件: {task.get('audio_files', '无')}")
                    print(f"- 播放列表URL: {task.get('playlist_url', '无')}")
                    print(f"- 总时长: {task.get('total_duration', '无')}")
                    
                    # 分析下载按钮显示逻辑
                    status = task.get('status', '')
                    has_audio = bool(task.get('audio_files') or task.get('playlist_url'))
                    should_show = status == 'completed' and has_audio
                    
                    print(f"\n🔍 下载按钮显示逻辑分析:")
                    print(f"- 状态是否为'completed': {status == 'completed'} (当前: {status})")
                    print(f"- 是否有音频文件: {has_audio}")
                    print(f"- 应该显示下载按钮: {should_show}")
                    
                    if not should_show:
                        print(f"\n❌ 下载按钮不显示的原因:")
                        if status != 'completed':
                            print(f"  - 任务状态不是'completed'，当前状态: '{status}'")
                        if not has_audio:
                            print(f"  - 没有音频文件或播放列表URL")
                            
            except json.JSONDecodeError:
                print("❌ 响应数据不是有效的JSON格式")
                
        else:
            print(f"❌ 获取任务数据失败: {tasks_response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务，请确保应用程序正在运行")
        print("   启动命令: python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")

if __name__ == "__main__":
    check_api_response()
