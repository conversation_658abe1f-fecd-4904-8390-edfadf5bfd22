<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载按钮调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .task-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .task-actions {
            margin-top: 1rem;
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-outline {
            background: transparent;
            color: #007bff;
            border: 1px solid #007bff;
        }
        .btn-sm {
            padding: 6px 12px;
            font-size: 14px;
        }
        .status-completed {
            color: green;
            font-weight: bold;
        }
        .status-pending {
            color: orange;
            font-weight: bold;
        }
        .status-failed {
            color: red;
            font-weight: bold;
        }
        .debug-info {
            background: #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <h1>有声书项目 - 下载按钮调试页面</h1>
    
    <div class="debug-section">
        <h2>模拟任务数据测试</h2>
        <button onclick="testDownloadButtonLogic()">测试下载按钮显示逻辑</button>
        <button onclick="testWithRealData()">使用真实数据测试</button>
        <div id="test-results"></div>
    </div>

    <div class="debug-section">
        <h2>任务状态检查</h2>
        <button onclick="checkTaskStatuses()">检查任务状态映射</button>
        <div id="status-results"></div>
    </div>

    <script>
        // 模拟用户数据
        const mockUser = {
            token: 'mock-token-123'
        };

        // 模拟任务数据 - 包含不同状态和字段组合
        const mockTasks = [
            {
                id: 1000,
                title: "Dortha_Ledner",
                status: "completed",  // 前端期望的状态
                audioUrl: null,
                audio_url: null,
                playlistUrl: null,
                playlist_url: null,
                downloadUrl: null,
                download_url: null,
                audioPath: null,
                audio_path: null
            },
            {
                id: 1001,
                title: "Test Task with audioUrl",
                status: "completed",
                audioUrl: "/api/audio/1001.mp3",
                audio_url: null,
                playlistUrl: null,
                playlist_url: null,
                downloadUrl: null,
                download_url: null,
                audioPath: null,
                audio_path: null
            },
            {
                id: 1002,
                title: "Test Task with downloadUrl",
                status: "completed",
                audioUrl: null,
                audio_url: null,
                playlistUrl: null,
                playlist_url: null,
                downloadUrl: "/api/audio/1002/download",
                download_url: null,
                audioPath: null,
                audio_path: null
            },
            {
                id: 1003,
                title: "Test Task - Pending Status",
                status: "pending",
                audioUrl: null,
                audio_url: null,
                playlistUrl: null,
                playlist_url: null,
                downloadUrl: null,
                download_url: null,
                audioPath: null,
                audio_path: null
            }
        ];

        // 复制前端的 hasAudioFiles 方法
        function hasAudioFiles(task) {
            console.log('检查任务音频文件:', task);
            
            // 检查各种可能的音频URL字段
            if (task.audioUrl || task.audio_url) {
                console.log('找到 audioUrl:', task.audioUrl || task.audio_url);
                return true;
            }
            
            // 检查播放列表URL（增强版音频）
            if (task.playlistUrl || task.playlist_url) {
                console.log('找到 playlistUrl:', task.playlistUrl || task.playlist_url);
                return true;
            }
            
            // 检查音频路径
            if (task.audioPath || task.audio_path) {
                console.log('找到 audioPath:', task.audioPath || task.audio_path);
                return true;
            }
            
            // 检查下载URL
            if (task.downloadUrl || task.download_url) {
                console.log('找到 downloadUrl:', task.downloadUrl || task.download_url);
                return true;
            }
            
            // 对于已完成的任务，假设都有音频文件（通过API流式传输）
            if (task.status === 'completed') {
                console.log('任务已完成，假设有音频文件');
                return true;
            }
            
            console.log('未找到音频文件');
            return false;
        }

        // 复制前端的 getAudioDownloadUrl 方法
        function getAudioDownloadUrl(task) {
            console.log('获取音频下载URL:', task);
            
            // 优先使用明确的下载URL
            if (task.downloadUrl) {
                console.log('使用 downloadUrl:', task.downloadUrl);
                return task.downloadUrl;
            }

            if (task.download_url) {
                console.log('使用 download_url:', task.download_url);
                return task.download_url;
            }

            // 其次使用音频URL
            if (task.audioUrl) {
                console.log('使用 audioUrl:', task.audioUrl);
                return task.audioUrl;
            }

            if (task.audio_url) {
                console.log('使用 audio_url:', task.audio_url);
                return task.audio_url;
            }

            // 对于已完成的任务，提供API下载链接（带token参数）
            if (task.status === 'completed' && mockUser && mockUser.token) {
                const url = `/api/audio/${task.id}/download?token=${encodeURIComponent(mockUser.token)}`;
                console.log('生成API下载链接:', url);
                return url;
            }

            console.log('无法生成下载URL');
            return null;
        }

        // 测试下载按钮显示逻辑
        function testDownloadButtonLogic() {
            const resultsDiv = document.getElementById('test-results');
            let html = '<h3>下载按钮显示逻辑测试结果:</h3>';
            
            mockTasks.forEach(task => {
                const hasAudio = hasAudioFiles(task);
                const downloadUrl = getAudioDownloadUrl(task);
                const shouldShowButton = task.status === 'completed' && hasAudio;
                const shouldShowDownloadLink = shouldShowButton && downloadUrl;
                
                html += `
                    <div class="task-item">
                        <strong>任务 ${task.id}: ${task.title}</strong><br>
                        <div class="debug-info">
                            状态: <span class="status-${task.status}">${task.status}</span><br>
                            hasAudioFiles(): <strong>${hasAudio ? '是' : '否'}</strong><br>
                            getAudioDownloadUrl(): <strong>${downloadUrl || '无'}</strong><br>
                            应该显示操作按钮: <strong>${shouldShowButton ? '是' : '否'}</strong><br>
                            应该显示下载链接: <strong>${shouldShowDownloadLink ? '是' : '否'}</strong>
                        </div>
                        
                        ${shouldShowButton ? `
                            <div class="task-actions">
                                <button class="btn btn-primary btn-sm">
                                    <i class="fas fa-play"></i> 播放音频
                                </button>
                                ${downloadUrl ? `
                                    <a href="${downloadUrl}" class="btn btn-outline btn-sm" download>
                                        <i class="fas fa-download"></i> 下载音频
                                    </a>
                                ` : '<span style="color: red;">❌ 下载按钮不显示（无下载URL）</span>'}
                            </div>
                        ` : '<span style="color: red;">❌ 操作按钮不显示（状态或音频文件检查失败）</span>'}
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }

        // 检查任务状态映射
        function checkTaskStatuses() {
            const resultsDiv = document.getElementById('status-results');
            
            const statusMappings = [
                { backend: 'completed', frontend: 'completed', match: true },
                { backend: 'SUCCESS', frontend: 'completed', match: false },
                { backend: 'pending', frontend: 'pending', match: true },
                { backend: 'processing', frontend: 'pending', match: false },
                { backend: 'failed', frontend: 'failed', match: true }
            ];
            
            let html = '<h3>任务状态映射检查:</h3>';
            html += '<table border="1" style="border-collapse: collapse; width: 100%;">';
            html += '<tr><th>后端状态</th><th>前端期望</th><th>匹配</th><th>影响</th></tr>';
            
            statusMappings.forEach(mapping => {
                const color = mapping.match ? 'green' : 'red';
                const impact = mapping.match ? '✅ 正常' : '❌ 可能导致按钮不显示';
                
                html += `
                    <tr style="color: ${color};">
                        <td>${mapping.backend}</td>
                        <td>${mapping.frontend}</td>
                        <td>${mapping.match ? '是' : '否'}</td>
                        <td>${impact}</td>
                    </tr>
                `;
            });
            
            html += '</table>';
            html += '<p><strong>结论:</strong> 如果后端返回 "SUCCESS" 而前端检查 "completed"，会导致下载按钮不显示。</p>';
            
            resultsDiv.innerHTML = html;
        }

        // 使用真实数据测试（需要在实际环境中运行）
        async function testWithRealData() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<p>正在获取真实任务数据...</p>';
            
            try {
                // 这里需要实际的API调用
                const response = await fetch('/api/tasks', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
                    }
                });
                
                if (response.ok) {
                    const tasks = await response.json();
                    console.log('真实任务数据:', tasks);
                    
                    let html = '<h3>真实任务数据测试结果:</h3>';
                    
                    if (tasks && tasks.length > 0) {
                        tasks.slice(0, 5).forEach(task => {  // 只测试前5个任务
                            const hasAudio = hasAudioFiles(task);
                            const downloadUrl = getAudioDownloadUrl(task);
                            const shouldShowButton = task.status === 'completed' && hasAudio;
                            
                            html += `
                                <div class="task-item">
                                    <strong>任务 ${task.id}: ${task.title || task.original_filename || '未知'}</strong><br>
                                    <div class="debug-info">
                                        状态: <span class="status-${task.status}">${task.status}</span><br>
                                        任务类型: ${task.task_type || '未知'}<br>
                                        hasAudioFiles(): <strong>${hasAudio ? '是' : '否'}</strong><br>
                                        getAudioDownloadUrl(): <strong>${downloadUrl || '无'}</strong><br>
                                        应该显示下载按钮: <strong>${shouldShowButton ? '是' : '否'}</strong>
                                    </div>
                                </div>
                            `;
                        });
                    } else {
                        html += '<p>没有找到任务数据</p>';
                    }
                    
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = '<p style="color: red;">获取任务数据失败: ' + response.status + '</p>';
                }
            } catch (error) {
                console.error('获取真实数据失败:', error);
                resultsDiv.innerHTML = '<p style="color: red;">获取真实数据失败: ' + error.message + '</p>';
            }
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            console.log('页面加载完成，开始调试测试...');
            testDownloadButtonLogic();
            checkTaskStatuses();
        };
    </script>
</body>
</html>
