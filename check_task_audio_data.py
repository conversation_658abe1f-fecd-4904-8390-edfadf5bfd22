#!/usr/bin/env python3
"""
检查任务音频数据脚本
查看任务数据中的音频相关字段
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from sqlalchemy.orm import sessionmaker
from app.core.database import engine
from app.models.task import Task
from app.models.user import User

def check_task_audio_data():
    """检查任务音频数据"""
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    with SessionLocal() as db:
        print("=== 检查任务音频数据 ===")
        
        # 查找所有已完成的任务
        completed_tasks = db.query(Task).filter(Task.status == 'completed').all()
        
        print(f"找到 {len(completed_tasks)} 个已完成任务:")
        
        for task in completed_tasks:
            print(f"\n任务ID: {task.id}")
            print(f"标题: {task.title}")
            print(f"用户ID: {task.user_id}")
            print(f"状态: {task.status.value}")
            print(f"创建时间: {task.created_at}")
            
            # 检查音频相关字段
            print("音频相关字段:")
            print(f"  - audioUrl: {getattr(task, 'audioUrl', 'N/A')}")
            print(f"  - audio_url: {getattr(task, 'audio_url', 'N/A')}")
            print(f"  - audioPath: {getattr(task, 'audioPath', 'N/A')}")
            print(f"  - audio_path: {getattr(task, 'audio_path', 'N/A')}")
            print(f"  - downloadUrl: {getattr(task, 'downloadUrl', 'N/A')}")
            print(f"  - download_url: {getattr(task, 'download_url', 'N/A')}")
            print(f"  - playlistUrl: {getattr(task, 'playlistUrl', 'N/A')}")
            print(f"  - playlist_url: {getattr(task, 'playlist_url', 'N/A')}")
            print(f"  - result: {getattr(task, 'result', 'N/A')}")
            
            # 检查任务目录是否存在音频文件
            task_dir = Path(f"data/users/{task.user_id}/tasks/{task.id}")
            print(f"任务目录: {task_dir}")
            print(f"目录存在: {task_dir.exists()}")
            
            if task_dir.exists():
                audio_files = []
                for ext in ['*.mp3', '*.wav', '*.m4a', '*.aac']:
                    audio_files.extend(task_dir.glob(ext))
                    audio_files.extend(task_dir.glob(f"**/{ext}"))
                
                print(f"音频文件: {[str(f.relative_to(task_dir)) for f in audio_files]}")
                
                # 检查是否有播放列表文件
                playlist_files = list(task_dir.glob("**/playlist.json")) + list(task_dir.glob("playlist.json"))
                print(f"播放列表文件: {[str(f.relative_to(task_dir)) for f in playlist_files]}")
            
            print("-" * 50)

if __name__ == "__main__":
    try:
        check_task_audio_data()
    except Exception as e:
        print(f"\n❌ 检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
